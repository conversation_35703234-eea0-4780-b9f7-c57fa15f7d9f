apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: PLACEHOLDER_PROJECT_ID-dev

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml

components:
- ../../components/common-labels
- ../../components/database-init

labels:
- pairs:
    app: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/component: PLACEHOLDER_APPLICATION_TYPE
    app.kubernetes.io/version: PLACEH<PERSON>DER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
    environment: dev
    source.repo: PLACEHOLDER_SOURCE_REPO_LABEL
    source.branch: PLACEHOLDER_SOURCE_BRANCH_LABEL

# Environment-specific configurations are now directly in the manifest files
# Database init container environment-specific patch is handled by the database-init component

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
- path: database-init-dev-patch.yaml
  target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
- path: jvm-optimization-patch.yaml
  target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID

namePrefix: ""
nameSuffix: "-dev"
