apiVersion: apps/v1
kind: Deployment
metadata:
  name: PLACEHOLDER_PROJECT_ID
spec:
  template:
    spec:
      containers:
      - name: PLACEH<PERSON>DER_PROJECT_ID
        env:
        # JVM optimization for dev environment
        - name: JAVA_OPTS
          value: "-Xms256m -Xmx512m -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heapdump.hprof"
        # Spring Boot development optimizations
        - name: SPRING_JPA_HIBERNATE_DDL_AUTO
          value: "update"    # Allow schema updates in dev
        - name: SPRING_JPA_SHOW_SQL
          value: "true"      # Enable SQL logging for debugging
        - name: LOGGING_LEVEL_ORG_SPRINGFRAMEWORK
          value: "INFO"      # More verbose logging for dev
        - name: LOGGING_LEVEL_ORG_HIBERNATE
          value: "INFO"      # Hibernate logging for dev debugging
