apiVersion: apps/v1
kind: Deployment
metadata:
  name: <PERSON><PERSON>CEHOLDER_PROJECT_ID
spec:
  template:
    spec:
      initContainers:
      - name: wait-for-database
        env:
        - name: ENVIRONMENT
          value: "dev"
        - name: TEST_CONNECTION
          value: "true"  # Enable connection testing in dev environment
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: PLACEH<PERSON><PERSON>R_PROJECT_ID-secrets-dev
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets-dev
              key: DB_PORT
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets-dev
              key: DB_USER
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: PLACEH<PERSON>DER_PROJECT_ID-secrets-dev
              key: DB_NAME
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: PLACEHOLDER_PROJECT_ID-secrets-dev
              key: DB_PASSWORD
        - name: <PERSON><PERSON><PERSON><PERSON><PERSON>
          valueFrom:
            secretKeyRef:
              name: PLACEH<PERSON><PERSON>R_PROJECT_ID-secrets-dev
              key: DB_SSL_MODE
