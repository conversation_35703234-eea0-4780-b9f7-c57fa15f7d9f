#!/usr/bin/env python3
"""
Test script to verify that environment-specific deployments preserve other environments
"""

import json
import subprocess
import sys
from pathlib import Path
import tempfile
import shutil

def print_status(message, status_type="INFO"):
    """Print colored status messages"""
    colors = {
        "SUCCESS": "\033[32m[SUCCESS]",
        "ERROR": "\033[31m[ERROR]",
        "WARNING": "\033[33m[WARNING]",
        "INFO": "\033[36m[INFO]"
    }
    reset = "\033[0m"
    color = colors.get(status_type, "")
    print(f"{color} {message}{reset}")

def check_file_content(file_path, expected_patterns, environment):
    """Check if file contains expected patterns"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        for pattern, should_exist in expected_patterns.items():
            exists = pattern in content
            if should_exist and not exists:
                print_status(f"❌ {environment}: Missing expected pattern '{pattern}' in {file_path}", "ERROR")
                return False
            elif not should_exist and exists:
                print_status(f"❌ {environment}: Found unexpected pattern '{pattern}' in {file_path}", "ERROR")
                return False
            elif should_exist and exists:
                print_status(f"✅ {environment}: Found expected pattern '{pattern}' in {file_path}", "SUCCESS")
        
        return True
    except Exception as e:
        print_status(f"❌ Error checking {file_path}: {e}", "ERROR")
        return False

def test_environment_preservation():
    """Test that deployments preserve other environment configurations"""
    print_status("🧪 Testing Environment Preservation Fix", "INFO")
    print_status("=" * 60, "INFO")
    
    project_id = "ai-spring-backend-test"
    
    # Check current state before test
    print_status("📋 Checking current state before test...", "INFO")
    
    dev_secret = Path(f"deployments/{project_id}/overlays/dev/secret.yaml")
    staging_secret = Path(f"deployments/{project_id}/overlays/staging/secret.yaml")
    production_secret = Path(f"deployments/{project_id}/overlays/production/secret.yaml")
    
    if not all([dev_secret.exists(), staging_secret.exists(), production_secret.exists()]):
        print_status("❌ Project overlay files not found. Please ensure the project exists.", "ERROR")
        return False
    
    # Check current state
    print_status("Current state:", "INFO")
    
    # Dev should have placeholders
    dev_patterns = {
        "PLACEHOLDER_PROJECT_ID": True,  # Should have placeholders
        "ai-spring-backend-test": False  # Should NOT have actual values
    }
    
    # Staging should have actual values
    staging_patterns = {
        "PLACEHOLDER_PROJECT_ID": False,  # Should NOT have placeholders
        "ai-spring-backend-test": True    # Should have actual values
    }
    
    # Production should have placeholders
    production_patterns = {
        "PLACEHOLDER_PROJECT_ID": True,  # Should have placeholders
        "ai-spring-backend-test": False  # Should NOT have actual values
    }
    
    print_status("Checking dev environment (should have placeholders):", "INFO")
    dev_ok = check_file_content(dev_secret, dev_patterns, "dev")
    
    print_status("Checking staging environment (should have actual values):", "INFO")
    staging_ok = check_file_content(staging_secret, staging_patterns, "staging")
    
    print_status("Checking production environment (should have placeholders):", "INFO")
    production_ok = check_file_content(production_secret, production_patterns, "production")
    
    if not all([dev_ok, staging_ok, production_ok]):
        print_status("❌ Current state verification failed", "ERROR")
        return False
    
    print_status("✅ Current state verified - staging has values, dev/prod have placeholders", "SUCCESS")
    print_status("", "INFO")
    
    # Now simulate a production deployment to test if it preserves dev and staging
    print_status("🚀 Simulating production deployment to test preservation...", "INFO")
    
    # Create test payload for production deployment
    test_payload = {
        "app_name": project_id,
        "project_id": project_id,
        "application_type": "springboot-backend",
        "environment": "production",
        "docker_image": "registry.digitalocean.com/chidhagni-registry/ai-spring-backend-test",
        "docker_tag": "production",
        "source_repo": "ChidhagniConsulting/ai-spring-backend-test",
        "source_branch": "main",
        "commit_sha": "test123456",
        "secrets_encoded": "eyJKV1RfU0VDUkVUIjoidGVzdC1qd3Qtc2VjcmV0IiwiREJfVVNFUiI6InRlc3QtdXNlciIsIkRCX1BBU1NXT1JEIjoidGVzdC1wYXNzd29yZCIsIkRCX0hPU1QiOiJ0ZXN0LWhvc3QiLCJEQl9QT1JUIjoiNTQzMiIsIkRCX05BTUUiOiJ0ZXN0LWRiIiwiREJfU1NMX01PREUiOiJyZXF1aXJlIn0="
    }
    
    # Write payload to temp file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(test_payload, f, indent=2)
        payload_file = f.name
    
    try:
        # Run deployment with dry-run to test the logic
        print_status(f"Running deployment script with payload...", "INFO")
        
        cmd = [
            "py", "scripts/deploy.py",
            "--payload", json.dumps(test_payload),
            "--manifest-dir", "manifests",
            "--output-dir", "test-output",
            "--dry-run"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
        
        if result.returncode != 0:
            print_status(f"❌ Deployment script failed: {result.stderr}", "ERROR")
            return False
        
        print_status("✅ Deployment script completed successfully", "SUCCESS")
        print_status("", "INFO")
        
        # Check if environments are preserved after the simulated deployment
        print_status("🔍 Checking environment preservation after production deployment...", "INFO")
        
        # Dev should still have placeholders (preserved)
        print_status("Checking dev environment (should still have placeholders):", "INFO")
        dev_preserved = check_file_content(dev_secret, dev_patterns, "dev")
        
        # Staging should still have actual values (preserved)
        print_status("Checking staging environment (should still have actual values):", "INFO")
        staging_preserved = check_file_content(staging_secret, staging_patterns, "staging")
        
        # Production should now have actual values (updated)
        production_updated_patterns = {
            "PLACEHOLDER_PROJECT_ID": False,  # Should NOT have placeholders anymore
            "ai-spring-backend-test": True    # Should have actual values now
        }
        print_status("Checking production environment (should now have actual values):", "INFO")
        production_updated = check_file_content(production_secret, production_updated_patterns, "production")
        
        if all([dev_preserved, staging_preserved, production_updated]):
            print_status("", "INFO")
            print_status("🎉 SUCCESS: Environment preservation test PASSED!", "SUCCESS")
            print_status("✅ Dev environment preserved (still has placeholders)", "SUCCESS")
            print_status("✅ Staging environment preserved (still has actual values)", "SUCCESS")
            print_status("✅ Production environment updated (now has actual values)", "SUCCESS")
            return True
        else:
            print_status("", "INFO")
            print_status("❌ FAILURE: Environment preservation test FAILED!", "ERROR")
            return False
            
    finally:
        # Clean up temp file
        Path(payload_file).unlink(missing_ok=True)
        
        # Clean up test output directory
        test_output = Path("test-output")
        if test_output.exists():
            shutil.rmtree(test_output)

if __name__ == "__main__":
    success = test_environment_preservation()
    sys.exit(0 if success else 1)
