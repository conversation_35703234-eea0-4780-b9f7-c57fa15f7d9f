apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-spring-backend-test
  labels:
    app: ai-spring-backend-test
    app.kubernetes.io/name: ai-spring-backend-test
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/part-of: ai-spring-backend-test
    app.kubernetes.io/version: d3091c4d
    app.kubernetes.io/managed-by: argocd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-spring-backend-test
  template:
    metadata:
      labels:
        app: ai-spring-backend-test
        app.kubernetes.io/name: ai-spring-backend-test
        app.kubernetes.io/component: springboot-backend
        app.kubernetes.io/part-of: ai-spring-backend-test
        app.kubernetes.io/version: d3091c4d
    spec:
      containers:
      - name: ai-spring-backend-test
        image: registry.digitalocean.com/doks-registry/ai-spring-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        envFrom:
        - configMapRef:
            name: ai-spring-backend-test-config
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: JWT_SECRET
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_NAME
        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_USER
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: DB_PASSWORD
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-test-secrets
              key: GOOGLE_CLIENT_SECRET
        livenessProbe:
          tcpSocket:
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
