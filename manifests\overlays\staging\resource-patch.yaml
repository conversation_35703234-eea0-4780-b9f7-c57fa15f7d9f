apiVersion: v1
kind: ResourceQuota
metadata:
  name: staging-resource-quota
  namespace: PLACEHOLDER_PROJECT_ID-staging
spec:
  hard:
    # CPU and Memory quotas for staging environment
    requests.cpu: "3"      # Total CPU requests (2 replicas * 750m each + overhead)
    requests.memory: 3Gi   # Total memory requests (2 replicas * 1Gi each + overhead)
    limits.cpu: "6"        # Total CPU limits (2 replicas * 2 cores each + overhead)
    limits.memory: 6Gi     # Total memory limits (2 replicas * 2Gi each + overhead)
    
    # Storage and pod limits
    persistentvolumeclaims: "4"
    pods: "10"
    
    # Additional resource limits for staging
    services: "5"
    secrets: "10"
    configmaps: "10"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: staging-container-limits
  namespace: PLACEHOLDER_PROJECT_ID-staging
spec:
  limits:
  - type: Container
    default:
      # Default limits if not specified
      cpu: "2"
      memory: "2Gi"
    defaultRequest:
      # Default requests if not specified
      cpu: "750m"
      memory: "1Gi"
    min:
      # Minimum resources required
      cpu: "100m"
      memory: "256Mi"
    max:
      # Maximum resources allowed per container
      cpu: "4"
      memory: "4Gi"
  - type: Pod
    max:
      # Maximum resources per pod
      cpu: "4"
      memory: "4Gi"
