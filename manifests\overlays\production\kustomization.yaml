apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: PLACEHOLDER_PROJECT_ID-production

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml
- resource-patch.yaml

components:
- ../../components/common-labels
- ../../components/database-init

labels:
- pairs:
    app: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/component: PLACEHOLDER_APPLICATION_TYPE
    app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
    environment: production
    source.repo: PLACEHOLDER_SOURCE_REPO_LABEL
    source.branch: PLACEHOLDER_SOURCE_BRANCH_LABEL

# Environment-specific configurations are now directly in the manifest files
# Database init container environment-specific patch is handled by the database-init component

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
- path: database-init-production-patch.yaml
  target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
- path: jvm-optimization-patch.yaml
  target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID

namePrefix: ""
nameSuffix: "-prod"
