# Manual Setup Steps Required

## Overview

The CI/CD pipeline has been implemented with the following workflows:
- ✅ **Staging Environment Deployment** (`.github/workflows/staging-deploy.yml`)
- ✅ **Production Environment Deployment** (`.github/workflows/prod-deploy.yml`)
- ✅ **Enhanced CODEOWNERS** protection for critical files
- ✅ **Documentation** for operational procedures

## Required Manual Steps

### 1. Configure GitHub Environments

You need to manually create and configure GitHub Environments with protection rules:

#### Create Staging Environment
1. Go to **Settings** → **Environments** in your GitHub repository
2. Click **New environment**
3. Name: `staging`
4. Configure protection rules:
   - ☐ **Required reviewers**: Add team members who can approve staging deployments
   - ☐ **Wait timer**: Optional (e.g., 5 minutes)
   - ☐ **Deployment branches**: Restrict to `main` branch only

#### Create Production Environment
1. Go to **Settings** → **Environments** in your GitHub repository
2. Click **New environment**
3. Name: `production`
4. Configure protection rules:
   - ☐ **Required reviewers**: Add `@AshrafSyed25` as required reviewer
   - ☐ **Wait timer**: Optional (e.g., 10 minutes for final review)
   - ☐ **Deployment branches**: Restrict to `main` branch only
   - ☐ **Environment secrets**: Configure production secrets (see below)

### 2. Configure Repository Secrets

#### Staging Environment Secrets
Navigate to **Settings** → **Secrets and variables** → **Actions** → **Environment secrets** → **staging**

Add the following secrets:
```
☐ JWT_SECRET_STAGING
☐ ENABLE_DATABASE_STAGING
☐ DB_HOST_STAGING
☐ DB_USER_STAGING
☐ DB_PASSWORD_STAGING
☐ DB_NAME_STAGING
☐ DB_PORT_STAGING
☐ DB_SSL_MODE_STAGING
☐ SMTP_USER_STAGING
☐ SMTP_PASS_STAGING
☐ GOOGLE_CLIENT_ID_STAGING
☐ GOOGLE_CLIENT_SECRET_STAGING
```

#### Production Environment Secrets
Navigate to **Settings** → **Secrets and variables** → **Actions** → **Environment secrets** → **production**

Add the following secrets:
```
☐ JWT_SECRET_PRODUCTION
☐ ENABLE_DATABASE_PRODUCTION
☐ DB_HOST_PRODUCTION
☐ DB_USER_PRODUCTION
☐ DB_PASSWORD_PRODUCTION
☐ DB_NAME_PRODUCTION
☐ DB_PORT_PRODUCTION
☐ DB_SSL_MODE_PRODUCTION
☐ SMTP_USER_PRODUCTION
☐ SMTP_PASS_PRODUCTION
☐ GOOGLE_CLIENT_ID_PRODUCTION
☐ GOOGLE_CLIENT_SECRET_PRODUCTION
```

### 3. Configure Repository Variables

Navigate to **Settings** → **Secrets and variables** → **Actions** → **Variables**

Add the following repository variables:
```
☐ STAGING_CLUSTER_ID - Your DigitalOcean Kubernetes cluster ID for staging
☐ PRODUCTION_CLUSTER_ID - Your DigitalOcean Kubernetes cluster ID for production
```

### 4. Configure Branch Protection Rules

Navigate to **Settings** → **Branches** → **Add rule**

Configure protection for `main` branch:
```
☐ Branch name pattern: main
☐ Require a pull request before merging
☐ Require approvals: 1
☐ Dismiss stale PR approvals when new commits are pushed
☐ Require review from code owners
☐ Require status checks to pass before merging
☐ Require branches to be up to date before merging
☐ Require conversation resolution before merging
☐ Restrict pushes that create files that match a pattern (optional)
☐ Do not allow bypassing the above settings
☐ Restrict pushes that create files that match a pattern (optional)
```

### 5. Verify CODEOWNERS Configuration

The CODEOWNERS file has been updated to protect critical files. Verify it's working:

1. ☐ Create a test PR that modifies `.github/workflows/prod-deploy.yml`
2. ☐ Verify that @AshrafSyed25 is automatically requested as a reviewer
3. ☐ Test that the PR cannot be merged without approval

### 6. Test the Workflows

#### Test Staging Deployment
1. ☐ Create a test git tag: `git tag v0.1.0-test && git push origin v0.1.0-test`
2. ☐ Navigate to **Actions** → **Staging Environment Deployment**
3. ☐ Run workflow with test parameters
4. ☐ Verify deployment completes successfully

#### Test Production Deployment (AshrafSyed25 only)
1. ☐ Create a GitHub release for the test tag
2. ☐ Navigate to **Actions** → **Production Environment Deployment**
3. ☐ Run workflow with test parameters (only @AshrafSyed25 can do this)
4. ☐ Verify approval gate works correctly
5. ☐ Verify deployment completes successfully

### 7. Remove/Disable Old Workflows (Optional)

If you want to completely replace the existing automatic promotion system:

1. ☐ Rename or disable `.github/workflows/deploy-from-cicd.yaml`
2. ☐ Rename or disable `.github/workflows/promote-environment.yaml`
3. ☐ Update any application repositories that trigger these workflows

**Note**: Consider keeping the existing workflows temporarily for backward compatibility.

### 8. Update Application Repositories

For each application repository that needs to use this new pipeline:

1. ☐ Remove automatic GitOps triggers (if any)
2. ☐ Update CI/CD to build and tag Docker images properly
3. ☐ Ensure semantic versioning for git tags
4. ☐ Update documentation to reference new deployment process

### 9. Documentation and Training

1. ☐ Review the created documentation: `docs/startup-cicd-pipeline.md`
2. ☐ Train team members on new deployment process
3. ☐ Create runbooks for common operations
4. ☐ Set up monitoring and alerting for deployments

## Validation Checklist

After completing the manual steps, validate the setup:

### Staging Environment
- ☐ Can trigger staging deployment manually
- ☐ Requires valid git tag
- ☐ Validates Docker image exists
- ☐ Deploys successfully to staging cluster
- ☐ Environment secrets are properly injected

### Production Environment
- ☐ Only @AshrafSyed25 can trigger production deployment
- ☐ Requires GitHub release
- ☐ Requires deployment reason and rollback plan
- ☐ Approval gate works correctly
- ☐ Deploys successfully to production cluster
- ☐ Environment secrets are properly injected

### Security and Governance
- ☐ CODEOWNERS protection works for critical files
- ☐ Branch protection prevents direct pushes to main
- ☐ PR reviews are required for protected files
- ☐ Deployment audit logs are maintained

### Operational Procedures
- ☐ Team understands new deployment process
- ☐ Rollback procedures are documented and tested
- ☐ Monitoring and alerting are configured
- ☐ Emergency procedures are documented

## Support and Troubleshooting

If you encounter issues during setup:

1. **GitHub Environments**: Ensure you have admin permissions on the repository
2. **Secrets Configuration**: Double-check secret names match exactly (case-sensitive)
3. **Branch Protection**: May take a few minutes to take effect
4. **CODEOWNERS**: Ensure the file is in the root `.github/` directory
5. **Workflow Permissions**: Verify the `GITOPS_TOKEN` has necessary permissions

## Next Steps

After completing the manual setup:

1. **Monitor**: Watch the first few deployments closely
2. **Iterate**: Adjust workflows based on team feedback
3. **Enhance**: Add additional security and monitoring features
4. **Document**: Keep operational procedures up to date

## Contact

For questions or issues with this setup, contact:
- **Release Manager**: @AshrafSyed25
- **Documentation**: See `docs/startup-cicd-pipeline.md`
