apiVersion: apps/v1
kind: Deployment
metadata:
  name: <PERSON><PERSON>CEH<PERSON>DER_PROJECT_ID
  labels:
    app: PLA<PERSON>H<PERSON>DER_PROJECT_ID
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/component: database-init
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
spec:
  template:
    spec:
      initContainers:
      - name: wait-for-database
        image: postgres:13-alpine
        command: ['sh', '-c']
        args:
        - |
          # Database connection details are already decoded by Kubernetes from secrets
          # No need for base64 decoding as Kubernetes handles this automatically
          echo "🔍 Checking database connectivity..."
          echo "Database Host: $DB_HOST"
          echo "Database Port: $DB_PORT"
          echo "Database User: $DB_USER"
          echo "Database Name: $DB_NAME"

          # Wait for database to be ready
          until pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER"; do
            echo "⏳ Waiting for database to be ready..."
            sleep 2
          done

          echo "✅ Database is ready!"

          # Optional: Test database connection
          if [ "$TEST_CONNECTION" = "true" ]; then
            echo "🧪 Testing database connection..."
            psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null
            if [ $? -eq 0 ]; then
              echo "✅ Database connection test successful!"
            else
              echo "❌ Database connection test failed!"
              exit 1
            fi
          fi
        env:
        # Application metadata
        - name: APP_NAME
          value: "PLACEHOLDER_APP_NAME"
        - name: PROJECT_ID
          value: "PLACEHOLDER_PROJECT_ID"
        - name: APPLICATION_TYPE
          value: "PLACEHOLDER_APPLICATION_TYPE"
        # Environment-specific variables will be set by environment patches
        # Database connection details will be set by environment patches
