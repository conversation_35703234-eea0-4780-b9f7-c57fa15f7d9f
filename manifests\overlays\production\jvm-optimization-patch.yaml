apiVersion: apps/v1
kind: Deployment
metadata:
  name: PLACEHOLDER_PROJECT_ID
spec:
  template:
    spec:
      containers:
      - name: PLACEHOLDER_PROJECT_ID
        env:
        # JVM optimization for production environment
        - name: JAVA_OPTS
          value: "-Xms2g -Xmx3g -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heapdump.hprof -XX:+UseStringDeduplication -XX:G1HeapRegionSize=16m"
        # Spring Boot production optimizations
        - name: SPRING_JPA_HIBERNATE_DDL_AUTO
          value: "none"      # No schema changes in production
        - name: SPRING_JPA_SHOW_SQL
          value: "false"     # No SQL logging in production
        - name: LOGGING_LEVEL_ORG_SPRINGFRAMEWORK
          value: "WARN"      # Minimal Spring logging
        - name: LOG<PERSON><PERSON>_LEVEL_ORG_HIBERNATE
          value: "ERROR"     # Only error logging for Hibernate
        - name: LOGGING_LEVEL_ROOT
          value: "WARN"      # Minimal root logging
        # Production performance settings
        - name: SPRING_JPA_PROPERTIES_HIBERNATE_JDBC_BATCH_SIZE
          value: "25"        # Batch database operations
        - name: SPRING_JPA_PROPERTIES_HIBERNATE_ORDER_INSERTS
          value: "true"      # Optimize insert operations
        - name: SPRING_JPA_PROPERTIES_HIBERNATE_ORDER_UPDATES
          value: "true"      # Optimize update operations
