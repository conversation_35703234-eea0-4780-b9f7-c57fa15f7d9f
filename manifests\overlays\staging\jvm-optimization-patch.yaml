apiVersion: apps/v1
kind: Deployment
metadata:
  name: PLACEHOLDER_PROJECT_ID
spec:
  template:
    spec:
      containers:
      - name: PLACEHOLDER_PROJECT_ID
        env:
        # JVM optimization for staging environment
        - name: JAVA_OPTS
          value: "-Xms1g -Xmx1536m -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heapdump.hprof"
        # Spring Boot specific optimizations
        - name: SPRING_JPA_HIBERNATE_DDL_AUTO
          value: "validate"  # More conservative for staging
        - name: SPRING_JPA_SHOW_SQL
          value: "false"     # Reduce logging overhead
        - name: LOGGING_LEVEL_ORG_SPRINGFRAMEWORK
          value: "WARN"      # Reduce Spring logging
        - name: LOGGING_LEVEL_ORG_HIBERNATE
          value: "WARN"      # Reduce Hibernate logging
