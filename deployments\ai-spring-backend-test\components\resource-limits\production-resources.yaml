apiVersion: v1
kind: ResourceQuota
metadata:
  name: production-resource-quota
  namespace: ai-spring-backend-test-production
spec:
  hard:
    # CPU and Memory quotas for production environment
    requests.cpu: "6"      # Total CPU requests (3 replicas * 1.5 cores each + overhead)
    requests.memory: 9Gi   # Total memory requests (3 replicas * 2Gi each + overhead)
    limits.cpu: "12"       # Total CPU limits (3 replicas * 3 cores each + overhead)
    limits.memory: 18Gi    # Total memory limits (3 replicas * 4Gi each + overhead)
    
    # Storage and pod limits
    persistentvolumeclaims: "6"
    pods: "15"
    
    # Additional resource limits for production
    services: "8"
    secrets: "15"
    configmaps: "15"
    
    # Production-specific limits
    replicationcontrollers: "5"
    resourcequotas: "2"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: production-container-limits
  namespace: ai-spring-backend-test-production
spec:
  limits:
  - type: Container
    default:
      # Default limits if not specified
      cpu: "3"
      memory: "4Gi"
    defaultRequest:
      # Default requests if not specified
      cpu: "1500m"
      memory: "2Gi"
    min:
      # Minimum resources required
      cpu: "500m"
      memory: "1Gi"
    max:
      # Maximum resources allowed per container
      cpu: "6"
      memory: "8Gi"
  - type: Pod
    max:
      # Maximum resources per pod
      cpu: "6"
      memory: "8Gi"
