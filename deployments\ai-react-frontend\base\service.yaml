apiVersion: v1
kind: Service
metadata:
  name: ai-react-frontend-service
  labels:
    app: ai-react-frontend
    app.kubernetes.io/name: ai-react-frontend
    app.kubernetes.io/component: react-frontend
    app.kubernetes.io/part-of: AI React Frontend
    app.kubernetes.io/version: 53a0a299
    app.kubernetes.io/managed-by: argocd
spec:
  type: LoadBalancer
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: ai-react-frontend
