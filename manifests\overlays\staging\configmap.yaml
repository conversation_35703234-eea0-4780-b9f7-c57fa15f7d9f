apiVersion: v1
kind: ConfigMap
metadata:
  name: PLACEH<PERSON>DER_PROJECT_ID-config
  labels:
    app: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "PLACEHOLDER_APP_NAME"
  PROJECT_ID: "PLACEHOLDER_PROJECT_ID"
  APPLICATION_TYPE: "PLACEHOLDER_APPLICATION_TYPE"
  SOURCE_REPO: "PLACEHOLDER_SOURCE_REPO"
  SOURCE_BRANCH: "PLACEHOLDER_SOURCE_BRANCH"
  COMMIT_SHA: "PLACEH<PERSON><PERSON>R_COMMIT_SHA"
  NODE_ENV: "staging"
  PORT: "PLACEHOLDER_CONTAINER_PORT"
  
  # Database Configuration (DigitalOcean PostgreSQL)
  # These values will be replaced by GitHub Actions workflow from dispatch payload
  DB_HOST: "PLACEHOLDER_DB_HOST"
  DB_PORT: "PLACEHOLDER_DB_PORT"
  DB_NAME: "PLACEHOLDER_DB_NAME"
  DB_USER: "PLACEHOLDER_DB_USER"
  DB_SSL_MODE: "require"
  
  # Application URLs
  APP_URL: "http://localhost:PLACEHOLDER_CONTAINER_PORT"
  API_URL: "http://localhost:PLACEHOLDER_CONTAINER_PORT/api"
  
  # Common Backend Configuration
  SPRING_PROFILES_ACTIVE: "staging"
  SERVER_PORT: "PLACEHOLDER_CONTAINER_PORT"
  SPRING_APPLICATION_NAME: "app"

  # Spring Boot Database Configuration (Managed DigitalOcean Database)
  SPRING_DATASOURCE_URL: "*********************************************************************************************"
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"
  SPRING_DATASOURCE_USERNAME: "PLACEHOLDER_DB_USER"
  SPRING_JPA_HIBERNATE_DDL_AUTO: "update"
  SPRING_JPA_SHOW_SQL: "true"
  SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: "org.hibernate.dialect.PostgreSQLDialect"
  SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL: "true"

  # Spring Boot Management & Actuator
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,info,metrics,prometheus"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: "when-authorized"
  MANAGEMENT_HEALTH_PROBES_ENABLED: "true"
  
  # JVM Configuration
  JAVA_OPTS: "-Xms256m -Xmx512m -XX:+UseG1GC -XX:+UseContainerSupport"
  
  # Django Configuration
  DJANGO_SETTINGS_MODULE: "app.settings.staging"
  DEBUG: "False"
  ALLOWED_HOSTS: "localhost,127.0.0.1"
  
  # Django Static Files
  STATIC_URL: "/static/"
  STATIC_ROOT: "/app/staticfiles/"
  MEDIA_URL: "/media/"
  MEDIA_ROOT: "/app/media/"
  
  # Django CORS Configuration
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://127.0.0.1:3000"
  CORS_ALLOW_CREDENTIALS: "true"
  
  # NestJS Configuration
  APP_PORT: "PLACEHOLDER_CONTAINER_PORT"
  
  # React Frontend Configuration
  REACT_APP_API_URL: "http://localhost:PLACEHOLDER_CONTAINER_PORT/api"
  REACT_APP_ENVIRONMENT: "staging"
  REACT_APP_VERSION: "1.0.0"
  REACT_APP_TITLE: "My App"
  PUBLIC_URL: "/"
  GENERATE_SOURCEMAP: "false"
