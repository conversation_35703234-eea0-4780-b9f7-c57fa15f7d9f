apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ai-spring-backend-test-dev

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml

components:
- ../../components/common-labels
- ../../components/database-init

labels:
- pairs:
    app: ai-spring-backend-test
    app.kubernetes.io/name: ai-spring-backend-test
    app.kubernetes.io/part-of: ai-spring-backend-test
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: 908005c9
    app.kubernetes.io/managed-by: argocd
    environment: dev
    source.repo: ChidhagniConsulting-ai-spring-backend
    source.branch: 25-merge

# Environment-specific configurations are now directly in the manifest files
# Database init container environment-specific patch is handled by the database-init component

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: ai-spring-backend-test
- path: database-init-dev-patch.yaml
  target:
    kind: Deployment
    name: ai-spring-backend-test

namePrefix: ""
nameSuffix: "-dev"
