apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-spring-backend-test-config
  labels:
    app: ai-spring-backend-test
    app.kubernetes.io/name: ai-spring-backend-test
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: ai-spring-backend-test
    app.kubernetes.io/version: d3091c4d
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "ai-spring-backend-test"
  PROJECT_ID: "ai-spring-backend-test"
  APPLICATION_TYPE: "springboot-backend"
  SOURCE_REPO: "ChidhagniConsulting-ai-spring-backend"
  SOURCE_BRANCH: "25-merge"
  COMMIT_SHA: "d3091c4d"
  NODE_ENV: "staging"
  PORT: "8080"
  
  # Database Configuration (DigitalOcean PostgreSQL)
  # These values will be replaced by GitHub Actions workflow from dispatch payload
  DB_HOST: "private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com"
  DB_PORT: "25060"
  DB_NAME: "spring_staging_db"
  DB_USER: "spring_staging_user"
  DB_SSL_MODE: "require"
  
  # Application URLs
  APP_URL: "http://localhost:8080"
  API_URL: "http://localhost:8080/api"
  
  # Common Backend Configuration
  SPRING_PROFILES_ACTIVE: "staging"
  SERVER_PORT: "8080"
  SPRING_APPLICATION_NAME: "app"

  # Spring Boot Database Configuration (Managed DigitalOcean Database)
  SPRING_DATASOURCE_URL: "******************************************************************************************************************************"
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"
  SPRING_DATASOURCE_USERNAME: "spring_staging_user"
  SPRING_JPA_HIBERNATE_DDL_AUTO: "update"
  SPRING_JPA_SHOW_SQL: "true"
  SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: "org.hibernate.dialect.PostgreSQLDialect"
  SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL: "true"

  # Spring Boot Management & Actuator
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,info,metrics,prometheus"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: "when-authorized"
  MANAGEMENT_HEALTH_PROBES_ENABLED: "true"
  
  # JVM Configuration
  JAVA_OPTS: "-Xms256m -Xmx512m -XX:+UseG1GC -XX:+UseContainerSupport"
  
  # Django Configuration
  DJANGO_SETTINGS_MODULE: "app.settings.staging"
  DEBUG: "False"
  ALLOWED_HOSTS: "localhost,127.0.0.1"
  
  # Django Static Files
  STATIC_URL: "/static/"
  STATIC_ROOT: "/app/staticfiles/"
  MEDIA_URL: "/media/"
  MEDIA_ROOT: "/app/media/"
  
  # Django CORS Configuration
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://127.0.0.1:3000"
  CORS_ALLOW_CREDENTIALS: "true"
  
  # NestJS Configuration
  APP_PORT: "8080"
  
  # React Frontend Configuration
  REACT_APP_API_URL: "http://localhost:8080/api"
  REACT_APP_ENVIRONMENT: "staging"
  REACT_APP_VERSION: "1.0.0"
  REACT_APP_TITLE: "My App"
  PUBLIC_URL: "/"
  GENERATE_SOURCEMAP: "false"
